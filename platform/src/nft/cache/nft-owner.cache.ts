import { Inject, Singleton } from '../../core/ioc';
import { <PERSON>acheKeyGenerator } from '../../core/cache';
import { JsonSerialiser, Redis } from '../../core';
import moment from 'moment';
import { NFT } from '../nft';
import { BlockchainNetwork } from '../../blockchain';

@Singleton
export class NFTOwnerCache {
    private readonly CACHE_EXPIRY_MINS = 60;
    private readonly cacheKeyGenerator = new CacheKeyGenerator('NFT:Owner');

    constructor(
        @Inject private readonly redis: Redis,
        @Inject private readonly serialiser: JsonSerialiser) {
    }

    public async getAll(owner: string, address: string, network: BlockchainNetwork): Promise<NFT[]> {
        const cacheKey = this.cacheKeyGenerator.generate(owner, address, network);
        const rawItems = await this.redis.cluster.smembers(cacheKey);

        if (!rawItems || rawItems.length === 0)
            return [];

        return rawItems.map(v => this.serialiser.deserialise<NFT>(v));
    }

    public async store(owner: string, address: string, network: BlockchainNetwork, ...nfts: NFT[]): Promise<void> {
        const cacheKey = this.cacheKeyGenerator.generate(owner, address, network);
        const expireAt = moment()
            .add(this.CACHE_EXPIRY_MINS, 'minutes')
            .unix();

        for (const nft of nfts) {
            if (nft.address !== address || nft.network !== network)
                continue;

            await this.redis.cluster
                .pipeline()
                .sadd(cacheKey, nft.tokenId, this.serialiser.serialise(nft))
                .expireat(cacheKey, expireAt)
                .exec();
        }
    }

    public async remove(owner: string, address: string, network: BlockchainNetwork, nft: NFT): Promise<void> {
        const cacheKey = this.cacheKeyGenerator.generate(owner, address, network);
        const expireAt = moment()
            .add(this.CACHE_EXPIRY_MINS, 'minutes')
            .unix();

        if (nft.address !== address || nft.network !== network)
            return;

        await this.redis.cluster
            .pipeline()
            .sadd(cacheKey, nft.tokenId, this.serialiser.serialise(nft))
            .expireat(cacheKey, expireAt)
            .exec();

    }
}