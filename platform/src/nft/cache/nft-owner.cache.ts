import { Inject, Singleton } from '../../core/ioc';
import { <PERSON>acheKeyGenerator } from '../../core/cache';
import { JsonSerialiser, Redis } from '../../core';
import { NFT } from '../nft';
import { BlockchainNetwork } from '../../blockchain';
import moment from 'moment';

@Singleton
export class NFTOwnerCache {
    private readonly CACHE_EXPIRY_MINS = 60;
    private readonly cacheKeyGenerator = new CacheKeyGenerator('NFT:Owner');

    constructor(
        @Inject private readonly redis: Redis,
        @Inject private readonly serialiser: JsonSerialiser) {
    }

    public async getAll(owner: string, address: string, network: BlockchainNetwork): Promise<NFT[]> {
        const cacheKey = this.cacheKeyGenerator.generate(owner, address, network);
        const rawItems = await this.redis.cluster.smembers(cacheKey);

        if (!rawItems || rawItems.length === 0)
            return [];

        return rawItems.map(v => this.serialiser.deserialise<NFT>(v));
    }

    public async store(owner: string, address: string, network: BlockchainNetwork, ...nfts: NFT[]): Promise<void> {
        const validNfts = nfts.filter(nft => nft.address === address && nft.network === network);

        if (validNfts.length === 0)
            return;

        const cacheKey = this.cacheKeyGenerator.generate(owner, address, network);
        const expireAt = moment()
            .add(this.CACHE_EXPIRY_MINS, 'minutes')
            .unix();

        const pipeline = this.redis.cluster.pipeline();

        for (const nft of validNfts)
            pipeline.sadd(cacheKey, this.serialiser.serialise(nft));

        pipeline.expireat(cacheKey, expireAt);

        await pipeline.exec();
    }

    public async remove(owner: string, address: string, network: BlockchainNetwork, nft: NFT): Promise<void> {
        if (nft.address !== address || nft.network !== network)
            return;

        const cacheKey = this.cacheKeyGenerator.generate(owner, address, network);

        await this.redis.cluster
            .pipeline()
            .srem(cacheKey, this.serialiser.serialise(nft))
            .exec();
    }
}