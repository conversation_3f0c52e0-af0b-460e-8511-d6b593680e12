import { describe, it } from '@tcom/test';
import { mock, instance, reset, when, verify, anything } from '@tcom/test/mock';
import { expect } from '@tcom/test/assert';
import { NFTManager } from '../../src/nft/nft-manager';
import { NFTRepository } from '../../src/nft/repositories/nft.repository';
import { PlatformEventDispatcher } from '../../src/core/events';
import { NFTOwnerCache } from '../../src/nft/cache';
import { NewNFT, NFT, NFTUpdate } from '../../src/nft/nft';
import { BlockchainNetwork } from '../../src/blockchain';
import { NotFoundError } from '../../src/core';

describe('NFTManager', () => {
    const mockRepository = mock(NFTRepository);
    const mockEventDispatcher = mock(PlatformEventDispatcher);
    const mockNftOwnerCache = mock(NFTOwnerCache);

    function getManager(): NFTManager {
        return new NFTManager(
            instance(mockRepository),
            instance(mockEventDispatcher),
            instance(mockNftOwnerCache)
        );
    }

    beforeEach(() => {
        reset(mockRepository);
        reset(mockEventDispatcher);
        reset(mockNftOwnerCache);
    });

    const sampleNFT: NFT = {
        id: 1,
        collectionId: 100,
        collectionName: 'Test Collection',
        address: '******************************************',
        network: BlockchainNetwork.Ethereum,
        tokenId: '123',
        name: 'Test NFT',
        imageUrl: 'https://example.com/image.png',
        previewUrl: 'https://example.com/preview.png',
        thumbnailUrl: 'https://example.com/thumb.png',
        rank: 1,
        rarity: 0.5,
        owner: '******************************************',
        ownerTags: ['$NFT:test'],
        traits: []
    };

    const sampleNewNFT: NewNFT = {
        collectionId: 100,
        tokenId: '123',
        name: 'Test NFT',
        imageUrl: 'https://example.com/image.png',
        previewUrl: 'https://example.com/preview.png',
        thumbnailUrl: 'https://example.com/thumb.png',
        rank: 1,
        rarity: 0.5,
        owner: '******************************************',
        ownerTags: ['$NFT:test'],
        traits: []
    };

    describe('add()', () => {
        it('should add NFT and store in cache when owner is provided', async () => {
            // Given
            const newNft = { ...sampleNewNFT };
            const addedNft = { ...sampleNFT, owner: newNft.owner?.toLowerCase() };

            when(mockRepository.add(anything())).thenResolve(addedNft);
            when(mockNftOwnerCache.store(anything(), anything(), anything(), anything())).thenResolve();
            when(mockEventDispatcher.send(anything(), anything())).thenResolve();

            const manager = getManager();

            // When
            const result = await manager.add(newNft);

            // Then
            expect(result).to.deep.equal(addedNft);
            verify(mockRepository.add(anything())).once();
            verify(mockNftOwnerCache.store(addedNft.owner!, addedNft.address, addedNft.network, addedNft)).once();
            verify(mockEventDispatcher.send(anything(), anything())).once();
        });

        it('should add NFT without caching when no owner is provided', async () => {
            // Given
            const newNft = { ...sampleNewNFT, owner: undefined };
            const addedNft = { ...sampleNFT, owner: undefined };

            when(mockRepository.add(anything())).thenResolve(addedNft);
            when(mockEventDispatcher.send(anything(), anything())).thenResolve();

            const manager = getManager();

            // When
            const result = await manager.add(newNft);

            // Then
            expect(result).to.deep.equal(addedNft);
            verify(mockRepository.add(anything())).once();
            verify(mockNftOwnerCache.store(anything(), anything(), anything(), anything())).never();
            verify(mockEventDispatcher.send(anything(), anything())).once();
        });

        it('should lowercase the owner address before adding', async () => {
            // Given
            const newNft = { ...sampleNewNFT, owner: '******************************************' };
            const addedNft = { ...sampleNFT, owner: '******************************************' };

            when(mockRepository.add(anything())).thenResolve(addedNft);
            when(mockNftOwnerCache.store(anything(), anything(), anything(), anything())).thenResolve();
            when(mockEventDispatcher.send(anything(), anything())).thenResolve();

            const manager = getManager();

            // When
            await manager.add(newNft);

            // Then
            verify(mockRepository.add(anything())).once();
            expect(newNft.owner).to.equal('******************************************');
        });
    });

    describe('update()', () => {
        it('should update NFT by ID and handle cache operations', async () => {
            // Given
            const nftId = 1;
            const update: NFTUpdate = {
                name: 'Updated NFT',
                owner: '0xNEWOWNER1234567890ABCDEF1234567890ABCDEF'
            };
            const currentNft = { ...sampleNFT };
            const updatedNft = { ...sampleNFT, ...update, owner: update.owner?.toLowerCase() };

            when(mockRepository.update(nftId, anything())).thenResolve(updatedNft);
            when(mockNftOwnerCache.remove(anything(), anything())).thenResolve();
            when(mockNftOwnerCache.store(anything(), anything(), anything(), anything())).thenResolve();
            when(mockEventDispatcher.send(anything())).thenResolve();

            const manager = getManager();

            // When
            const result = await manager.update(currentNft, update);

            // Then
            expect(result).to.deep.equal(updatedNft);
            verify(mockRepository.update(nftId, anything())).once();
            verify(mockNftOwnerCache.remove(currentNft.owner!, currentNft)).once();
            verify(mockNftOwnerCache.store(updatedNft.owner!, currentNft.address, currentNft.network, updatedNft)).once();
            verify(mockEventDispatcher.send(anything())).once();
        });

        it('should update NFT by ID when NFT object not provided', async () => {
            // Given
            const nftId = 1;
            const update: NFTUpdate = { name: 'Updated NFT' };
            const currentNft = { ...sampleNFT };
            const updatedNft = { ...sampleNFT, ...update };

            when(mockRepository.getById(nftId)).thenResolve(currentNft);
            when(mockRepository.update(nftId, anything())).thenResolve(updatedNft);
            when(mockNftOwnerCache.remove(anything(), anything())).thenResolve();
            when(mockEventDispatcher.send(anything(), anything())).thenResolve();

            const manager = getManager();

            // When
            const result = await manager.update(nftId, update);

            // Then
            expect(result).to.deep.equal(updatedNft);
            verify(mockRepository.getById(nftId)).once();
            verify(mockRepository.update(nftId, anything())).once();
        });

        it('should throw NotFoundError when NFT does not exist', async () => {
            // Given
            const nftId = 999;
            const update: NFTUpdate = { name: 'Updated NFT' };

            when(mockRepository.getById(nftId)).thenResolve(undefined);

            const manager = getManager();

            // When & Then
            await expect(manager.update(nftId, update)).to.be.rejectedWith(NotFoundError, 'NFT not found.');
        });
    });

    describe('setOwner()', () => {
        it('should set owner and update cache when NFT exists', async () => {
            // Given
            const nftId = 1;
            const newOwner = '0xNEWOWNER1234567890ABCDEF1234567890ABCDEF';
            const currentNft = { ...sampleNFT };

            when(mockRepository.getById(nftId)).thenResolve(currentNft);
            when(mockRepository.setOwner(nftId, newOwner.toLowerCase())).thenResolve();
            when(mockNftOwnerCache.remove(anything(), anything())).thenResolve();
            when(mockEventDispatcher.send(anything())).thenResolve();

            const manager = getManager();

            // When
            await manager.setOwner(nftId, newOwner);

            // Then
            verify(mockRepository.getById(nftId)).once();
            verify(mockRepository.setOwner(nftId, newOwner.toLowerCase())).once();
            verify(mockNftOwnerCache.remove(anything(), anything())).once();
            verify(mockEventDispatcher.send(anything())).once();
        });

        it('should store in cache when owner is same but ensure cache consistency', async () => {
            // Given
            const nftId = 1;
            const sameOwner = sampleNFT.owner!;
            const currentNft = { ...sampleNFT };

            when(mockRepository.getById(nftId)).thenResolve(currentNft);
            when(mockNftOwnerCache.store(anything(), anything(), anything(), anything())).thenResolve();

            const manager = getManager();

            // When
            await manager.setOwner(nftId, sameOwner);

            // Then
            verify(mockRepository.getById(nftId)).once();
            verify(mockRepository.setOwner(anything(), anything())).never();
            verify(mockNftOwnerCache.store(sameOwner, currentNft.address, currentNft.network, currentNft)).once();
            verify(mockEventDispatcher.send(anything())).never();
        });

        it('should throw NotFoundError when NFT does not exist', async () => {
            // Given
            const nftId = 999;
            const newOwner = '0xNEWOWNER1234567890ABCDEF1234567890ABCDEF';

            when(mockRepository.getById(nftId)).thenResolve(undefined);

            const manager = getManager();

            // When & Then
            await expect(manager.setOwner(nftId, newOwner)).to.be.rejectedWith(NotFoundError, 'NFT not found.');
        });

        it('should handle NFT with no current owner', async () => {
            // Given
            const nftId = 1;
            const newOwner = '0xNEWOWNER1234567890ABCDEF1234567890ABCDEF';
            const currentNft = { ...sampleNFT, owner: undefined };

            when(mockRepository.getById(nftId)).thenResolve(currentNft);
            when(mockRepository.setOwner(nftId, newOwner.toLowerCase())).thenResolve();
            when(mockEventDispatcher.send(anything())).thenResolve();

            const manager = getManager();

            // When
            await manager.setOwner(nftId, newOwner);

            // Then
            verify(mockRepository.setOwner(nftId, newOwner.toLowerCase())).once();
            verify(mockNftOwnerCache.remove(anything(), anything())).never();
            verify(mockEventDispatcher.send(anything())).once();
        });
    });

    describe('setOwnerByAddress()', () => {
        it('should set owner by address and update cache when NFT exists', async () => {
            // Given
            const address = '******************************************';
            const network = BlockchainNetwork.Ethereum;
            const tokenId = '123';
            const newOwner = '0xNEWOWNER1234567890ABCDEF1234567890ABCDEF';
            const currentNft = { ...sampleNFT };

            when(mockRepository.get(address.toLowerCase(), network, tokenId)).thenResolve(currentNft);
            when(mockRepository.setOwner(currentNft.id, newOwner.toLowerCase())).thenResolve();
            when(mockNftOwnerCache.remove(anything(), anything())).thenResolve();
            when(mockEventDispatcher.send(anything())).thenResolve();

            const manager = getManager();

            // When
            await manager.setOwnerByAddress(address, network, tokenId, newOwner);

            // Then
            verify(mockRepository.get(address.toLowerCase(), network, tokenId)).once();
            verify(mockRepository.setOwner(currentNft.id, newOwner.toLowerCase())).once();
            verify(mockNftOwnerCache.remove(anything(), anything())).once();
            verify(mockEventDispatcher.send(anything())).once();
        });

        it('should store in cache when owner is same', async () => {
            // Given
            const address = '******************************************';
            const network = BlockchainNetwork.Ethereum;
            const tokenId = '123';
            const sameOwner = sampleNFT.owner!;
            const currentNft = { ...sampleNFT };

            when(mockRepository.get(address.toLowerCase(), network, tokenId)).thenResolve(currentNft);
            when(mockNftOwnerCache.store(anything(), anything(), anything(), anything())).thenResolve();

            const manager = getManager();

            // When
            await manager.setOwnerByAddress(address, network, tokenId, sameOwner);

            // Then
            verify(mockRepository.get(address.toLowerCase(), network, tokenId)).once();
            verify(mockRepository.setOwner(anything(), anything())).never();
            verify(mockNftOwnerCache.store(sameOwner, currentNft.address, currentNft.network, currentNft)).once();
            verify(mockEventDispatcher.send(anything())).never();
        });

        it('should throw NotFoundError when NFT does not exist', async () => {
            // Given
            const address = '******************************************';
            const network = BlockchainNetwork.Ethereum;
            const tokenId = '999';
            const newOwner = '0xNEWOWNER1234567890ABCDEF1234567890ABCDEF';

            when(mockRepository.get(address.toLowerCase(), network, tokenId)).thenResolve(undefined);

            const manager = getManager();

            // When & Then
            await expect(manager.setOwnerByAddress(address, network, tokenId, newOwner))
                .to.be.rejectedWith(NotFoundError, 'NFT not found.');
        });
    });

    describe('getOwnerTokens()', () => {
        it('should return cached NFTs when available', async () => {
            // Given
            const owner = '0xowner1234567890abcdef1234567890abcdef12';
            const address = '******************************************';
            const network = BlockchainNetwork.Ethereum;
            const cachedNfts = [sampleNFT];

            when(mockNftOwnerCache.getAll(owner, address, network)).thenResolve(cachedNfts);

            const manager = getManager();

            // When
            const result = await manager.getOwnerTokens(owner, address, network);

            // Then
            expect(result).to.deep.equal(cachedNfts);
            verify(mockNftOwnerCache.getAll(owner, address, network)).once();
            verify(mockRepository.getOwnerTokens(anything(), anything(), anything())).never();
        });

        it('should fetch from repository and cache when no cached data', async () => {
            // Given
            const owner = '0xowner1234567890abcdef1234567890abcdef12';
            const address = '******************************************';
            const network = BlockchainNetwork.Ethereum;
            const repositoryNfts = [sampleNFT];

            when(mockNftOwnerCache.getAll(owner, address, network)).thenResolve([]);
            when(mockRepository.getOwnerTokens(owner, address, network)).thenResolve(repositoryNfts);
            when(mockNftOwnerCache.store(owner, address, network, ...repositoryNfts)).thenResolve();

            const manager = getManager();

            // When
            const result = await manager.getOwnerTokens(owner, address, network);

            // Then
            expect(result).to.deep.equal(repositoryNfts);
            verify(mockNftOwnerCache.getAll(owner, address, network)).once();
            verify(mockRepository.getOwnerTokens(owner, address, network)).once();
            verify(mockNftOwnerCache.store(owner, address, network, ...repositoryNfts)).once();
        });

        it('should return empty array when no NFTs found and not cache empty result', async () => {
            // Given
            const owner = '0xowner1234567890abcdef1234567890abcdef12';
            const address = '******************************************';
            const network = BlockchainNetwork.Ethereum;

            when(mockNftOwnerCache.getAll(owner, address, network)).thenResolve([]);
            when(mockRepository.getOwnerTokens(owner, address, network)).thenResolve([]);

            const manager = getManager();

            // When
            const result = await manager.getOwnerTokens(owner, address, network);

            // Then
            expect(result).to.deep.equal([]);
            verify(mockNftOwnerCache.getAll(owner, address, network)).once();
            verify(mockRepository.getOwnerTokens(owner, address, network)).once();
            verify(mockNftOwnerCache.store(anything(), anything(), anything())).never();
        });
    });
});